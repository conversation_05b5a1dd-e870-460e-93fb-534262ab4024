# MongoDB Replica Set Setup for Windows

## Method 1: Using MongoDB Compass (Easiest)

1. **Stop MongoDB Service**:
   ```cmd
   net stop MongoDB
   ```

2. **Start MongoDB with Replica Set**:
   ```cmd
   mongod --replSet rs0 --dbpath "C:\Program Files\MongoDB\Server\8.0\data"
   ```

3. **In another terminal, connect to MongoDB**:
   ```cmd
   mongosh
   ```

4. **Initialize Replica Set**:
   ```javascript
   rs.initiate({
     _id: "rs0",
     members: [{ _id: 0, host: "localhost:27017" }]
   })
   ```

5. **Verify Setup**:
   ```javascript
   rs.status()
   ```

## Method 2: Using Docker (Alternative)

1. **Create docker-compose.yml**:
   ```yaml
   version: '3.8'
   services:
     mongodb:
       image: mongo:7
       container_name: mongodb-replica
       command: mongod --replSet rs0 --bind_ip_all
       ports:
         - "27017:27017"
       volumes:
         - mongodb_data:/data/db
       environment:
         MONGO_INITDB_ROOT_USERNAME: admin
         MON<PERSON>O_INITDB_ROOT_PASSWORD: password

   volumes:
     mongodb_data:
   ```

2. **Start and Initialize**:
   ```bash
   docker-compose up -d
   docker exec -it mongodb-replica mongosh --eval "rs.initiate()"
   ```

## Method 3: MongoDB Atlas (Cloud - Recommended)

1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a free cluster
3. Get connection string
4. Update `.env.local`:
   ```env
   DATABASE_URL="mongodb+srv://username:<EMAIL>/webgenie?retryWrites=true&w=majority"
   ```

## After Setup

1. **Update auth.ts** to re-enable the adapter:
   ```typescript
   export const authOptions: NextAuthOptions = {
     adapter: PrismaAdapter(db) as any, // Uncomment this line
     // ... rest of config
   }
   ```

2. **Push database schema**:
   ```bash
   npx prisma db push
   ```

3. **Restart your application**
