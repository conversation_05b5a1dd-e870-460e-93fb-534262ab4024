import { withAuth } from "next-auth/middleware"

export default with<PERSON>uth(
  function middleware(req) {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Protect dashboard and project routes
        if (req.nextUrl.pathname.startsWith("/dashboard") || 
            req.nextUrl.pathname.startsWith("/project") ||
            req.nextUrl.pathname.startsWith("/projects")) {
          return !!token
        }
        return true
      },
    },
  }
)

export const config = {
  matcher: [
    "/dashboard/:path*",
    "/project/:path*", 
    "/projects/:path*"
  ]
}
