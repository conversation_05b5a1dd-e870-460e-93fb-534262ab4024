# WebGenie - AI Website Generator

Transform your ideas into stunning websites with the power of AI. No coding required, just pure creativity.

## 🚀 Features

- **AI-Powered Generation**: Create complete websites using Google Vertex AI (Gemini)
- **Live Code Editor**: Edit HTML, CSS, and JavaScript with Monaco Editor
- **Responsive Design**: All generated websites are mobile-first and responsive
- **Export Functionality**: Download your websites as ZIP files
- **Project Management**: Save, organize, and manage your projects
- **Dark Mode**: Beautiful dark and light themes
- **Authentication**: Secure login with NextAuth.js
- **Real-time Preview**: See your changes instantly

## 🛠️ Tech Stack

- **Frontend**: Next.js 14 (App Router), TypeScript, Tailwind CSS
- **UI Components**: ShadCN UI, Framer Motion for animations
- **Authentication**: NextAuth.js with email and OAuth providers
- **Database**: Supabase with Prisma ORM
- **AI Integration**: Google Vertex AI (Gemini) via Google Cloud AI Platform
- **Code Editor**: Monaco Editor
- **File Export**: JSZip

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/webgenie.git
   cd webgenie
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Fill in your environment variables:
   ```env
   # Vertex AI Configuration
   GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json
   GOOGLE_CLOUD_PROJECT=your-google-cloud-project-id
   VERTEX_AI_LOCATION=us-central1

   # Supabase Configuration
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your-supabase-anon-key
   DATABASE_URL=postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres

   # NextAuth Configuration
   NEXTAUTH_SECRET=your-nextauth-secret-key
   NEXTAUTH_URL=http://localhost:3000

   # OAuth Providers (Optional)
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   GITHUB_CLIENT_ID=your-github-client-id
   GITHUB_CLIENT_SECRET=your-github-client-secret
   ```

4. **Set up the database**
   ```bash
   npm run db:push
   npm run db:generate
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Usage

1. **Sign up/Login**: Create an account or sign in with email/OAuth
2. **Generate Website**: Describe your website idea in the prompt
3. **Preview**: View your generated website in real-time
4. **Edit Code**: Use the built-in Monaco editor to customize
5. **Export**: Download your website as a ZIP file
6. **Manage Projects**: Save and organize your creations

## 📁 Project Structure

```
webgenie/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── dashboard/         # Dashboard page
│   ├── login/            # Authentication pages
│   ├── project/          # Project editor and preview
│   └── projects/         # Projects listing
├── components/            # React components
│   ├── auth/             # Authentication components
│   ├── dashboard/        # Dashboard components
│   ├── editor/           # Code editor components
│   ├── landing/          # Landing page components
│   └── ui/               # ShadCN UI components
├── lib/                  # Utility libraries
│   ├── auth.ts           # NextAuth configuration
│   ├── db.ts             # Database connection
│   ├── openai.ts         # OpenAI integration
│   └── utils.ts          # Helper functions
├── prisma/               # Database schema
└── public/               # Static assets
```

## 🔧 Configuration

### Database Setup (Supabase)

1. Create a new Supabase project
2. Copy the database URL and anon key
3. Run the Prisma migrations to set up tables

### Vertex AI Setup

1. Create a Google Cloud Project
2. Enable the Vertex AI API
3. Create a service account and download the JSON key
4. Set the GOOGLE_APPLICATION_CREDENTIALS environment variable
5. The app uses Gemini 1.5 Pro for website generation

### Authentication Setup

1. Configure NextAuth.js providers
2. Set up OAuth apps (Google, GitHub)
3. Add provider credentials to environment variables

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [ShadCN UI](https://ui.shadcn.com/) for the beautiful component library
- [OpenAI](https://openai.com/) for the powerful GPT-4o API
- [Supabase](https://supabase.com/) for the backend infrastructure
- [Framer Motion](https://www.framer.com/motion/) for smooth animations

## 📞 Support

If you have any questions or need help, please:
- Open an issue on GitHub
- Join our Discord community
- Email <NAME_EMAIL>

---

Made with ❤️ for creators everywhere
