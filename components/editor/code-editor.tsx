"use client"

import { useState, useRef } from "react"
import { motion } from "framer-motion"
import Editor from "@monaco-editor/react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Play, 
  Save, 
  Download, 
  RefreshCw,
  Code,
  Eye,
  Smartphone,
  Monitor,
  Tablet
} from "lucide-react"
import { useTheme } from "next-themes"
import JSZip from "jszip"
import { useToast } from "@/hooks/use-toast"

interface CodeEditorProps {
  initialHtml?: string
  initialCss?: string
  initialJs?: string
  title?: string
  onSave?: (code: { html: string; css: string; js: string }) => void
}

export function CodeEditor({ 
  initialHtml = "", 
  initialCss = "", 
  initialJs = "", 
  title = "Untitled Project",
  onSave 
}: CodeEditorProps) {
  const [htmlCode, setHtmlCode] = useState(initialHtml)
  const [cssCode, setCssCode] = useState(initialCss)
  const [jsCode, setJsCode] = useState(initialJs)
  const [activeTab, setActiveTab] = useState("html")
  const [viewMode, setViewMode] = useState<"desktop" | "tablet" | "mobile">("desktop")
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const { theme } = useTheme()
  const { toast } = useToast()
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const generatePreviewCode = () => {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        ${cssCode}
    </style>
</head>
<body>
    ${htmlCode}
    <script>
        ${jsCode}
    </script>
</body>
</html>
    `.trim()
  }

  const handleSave = () => {
    if (onSave) {
      onSave({ html: htmlCode, css: cssCode, js: jsCode })
    }
    toast({
      title: "Project saved",
      description: "Your changes have been saved successfully.",
    })
  }

  const handleDownload = async () => {
    try {
      const zip = new JSZip()
      
      // Add files to zip
      zip.file("index.html", generatePreviewCode())
      if (cssCode.trim()) {
        zip.file("styles.css", cssCode)
      }
      if (jsCode.trim()) {
        zip.file("script.js", jsCode)
      }
      
      // Add README
      const readme = `# ${title}

This project was created with WebGenie.

## Files:
- index.html - Main HTML file
${cssCode.trim() ? "- styles.css - CSS styles" : ""}
${jsCode.trim() ? "- script.js - JavaScript code" : ""}

## Usage:
Open index.html in your web browser to view the website.

Created with ❤️ using WebGenie
`
      zip.file("README.md", readme)
      
      // Generate and download
      const content = await zip.generateAsync({ type: "blob" })
      const url = URL.createObjectURL(content)
      const link = document.createElement("a")
      link.href = url
      link.download = `${title.replace(/[^a-z0-9]/gi, "_")}.zip`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: "Download started",
        description: "Your project has been packaged and downloaded.",
      })
    } catch (error) {
      toast({
        title: "Download failed",
        description: "There was an error creating the download package.",
        variant: "destructive",
      })
    }
  }

  const refreshPreview = () => {
    if (iframeRef.current) {
      iframeRef.current.srcdoc = generatePreviewCode()
    }
  }

  const getIframeWidth = () => {
    switch (viewMode) {
      case "mobile":
        return "375px"
      case "tablet":
        return "768px"
      default:
        return "100%"
    }
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-background p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold">{title}</h1>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={refreshPreview}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={() => setIsPreviewMode(!isPreviewMode)}>
              {isPreviewMode ? <Code className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
              {isPreviewMode ? "Code" : "Preview"}
            </Button>
            <Button variant="outline" size="sm" onClick={handleDownload}>
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button size="sm" onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Code Editor */}
        <div className={`${isPreviewMode ? "hidden" : "w-1/2"} border-r`}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="html">HTML</TabsTrigger>
              <TabsTrigger value="css">CSS</TabsTrigger>
              <TabsTrigger value="js">JavaScript</TabsTrigger>
            </TabsList>
            
            <TabsContent value="html" className="flex-1 mt-0">
              <Editor
                height="100%"
                defaultLanguage="html"
                value={htmlCode}
                onChange={(value) => setHtmlCode(value || "")}
                theme={theme === "dark" ? "vs-dark" : "light"}
                options={{
                  minimap: { enabled: false },
                  fontSize: 14,
                  wordWrap: "on",
                  automaticLayout: true,
                }}
              />
            </TabsContent>
            
            <TabsContent value="css" className="flex-1 mt-0">
              <Editor
                height="100%"
                defaultLanguage="css"
                value={cssCode}
                onChange={(value) => setCssCode(value || "")}
                theme={theme === "dark" ? "vs-dark" : "light"}
                options={{
                  minimap: { enabled: false },
                  fontSize: 14,
                  wordWrap: "on",
                  automaticLayout: true,
                }}
              />
            </TabsContent>
            
            <TabsContent value="js" className="flex-1 mt-0">
              <Editor
                height="100%"
                defaultLanguage="javascript"
                value={jsCode}
                onChange={(value) => setJsCode(value || "")}
                theme={theme === "dark" ? "vs-dark" : "light"}
                options={{
                  minimap: { enabled: false },
                  fontSize: 14,
                  wordWrap: "on",
                  automaticLayout: true,
                }}
              />
            </TabsContent>
          </Tabs>
        </div>

        {/* Preview */}
        <div className={`${isPreviewMode ? "w-full" : "w-1/2"} flex flex-col`}>
          <div className="border-b p-2 flex items-center justify-between">
            <span className="text-sm font-medium">Preview</span>
            <div className="flex items-center gap-1 p-1 bg-muted rounded-lg">
              <Button
                variant={viewMode === "desktop" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("desktop")}
              >
                <Monitor className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === "tablet" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("tablet")}
              >
                <Tablet className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === "mobile" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("mobile")}
              >
                <Smartphone className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="flex-1 bg-gray-50 dark:bg-gray-900 p-4 flex justify-center">
            <div 
              className="transition-all duration-300 border bg-white shadow-lg"
              style={{ 
                width: getIframeWidth(),
                maxWidth: "100%",
                height: "100%"
              }}
            >
              <iframe
                ref={iframeRef}
                srcDoc={generatePreviewCode()}
                className="w-full h-full border-0"
                title="Preview"
                sandbox="allow-scripts allow-same-origin"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
