"use client"

import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Zap, 
  Code, 
  Download, 
  Palette, 
  Smartphone, 
  Globe,
  Sparkles,
  Edit3,
  Share2
} from "lucide-react"

const features = [
  {
    icon: Zap,
    title: "Lightning Fast",
    description: "Generate complete websites in seconds with our advanced AI technology.",
    color: "text-yellow-500"
  },
  {
    icon: Code,
    title: "Clean Code",
    description: "Get semantic, well-structured HTML with modern Tailwind CSS styling.",
    color: "text-blue-500"
  },
  {
    icon: Edit3,
    title: "Live Editor",
    description: "Edit your generated code with our built-in Monaco editor in real-time.",
    color: "text-green-500"
  },
  {
    icon: Smartphone,
    title: "Mobile First",
    description: "All generated websites are fully responsive and mobile-optimized.",
    color: "text-purple-500"
  },
  {
    icon: Download,
    title: "Export Ready",
    description: "Download your complete website as a ZIP file ready for deployment.",
    color: "text-indigo-500"
  },
  {
    icon: Palette,
    title: "Beautiful Design",
    description: "Modern, professional designs that look great out of the box.",
    color: "text-pink-500"
  },
  {
    icon: Globe,
    title: "SEO Optimized",
    description: "Generated websites include proper meta tags and semantic structure.",
    color: "text-teal-500"
  },
  {
    icon: Share2,
    title: "Easy Sharing",
    description: "Share your projects with others or collaborate on designs.",
    color: "text-orange-500"
  },
  {
    icon: Sparkles,
    title: "AI Powered",
    description: "Powered by GPT-4o for intelligent and creative website generation.",
    color: "text-cyan-500"
  }
]

export function FeaturesSection() {
  return (
    <section id="features" className="py-24 bg-gray-50 dark:bg-gray-900/50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            Everything You Need to
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {" "}Build Amazing Websites
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Our AI-powered platform provides all the tools you need to create, edit, and deploy 
            professional websites without any coding knowledge.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="h-full"
            >
              <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                <CardHeader>
                  <div className={`w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-4 ${feature.color}`}>
                    <feature.icon className="w-6 h-6" />
                  </div>
                  <CardTitle className="text-xl font-semibold">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
