"use client"

import { useState, useRef } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Download, 
  Code, 
  Eye, 
  Save, 
  ExternalLink,
  Smartphone,
  Monitor,
  Tablet
} from "lucide-react"
import J<PERSON><PERSON><PERSON> from "jszip"
import { downloadFile } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"

interface WebsitePreviewProps {
  htmlCode: string
  title?: string
}

export function WebsitePreview({ htmlCode, title }: WebsitePreviewProps) {
  const [viewMode, setViewMode] = useState<"desktop" | "tablet" | "mobile">("desktop")
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const { toast } = useToast()

  const handleDownload = async () => {
    try {
      const zip = new JSZip()
      
      // Add HTML file
      zip.file("index.html", htmlCode)
      
      // Add a simple README
      const readme = `# ${title || "Generated Website"}

This website was generated using WebGenie AI.

## Files included:
- index.html - The main HTML file

## How to use:
1. Open index.html in your web browser
2. Upload to your web hosting service
3. Customize as needed

Generated with ❤️ by WebGenie
`
      zip.file("README.md", readme)
      
      // Generate and download
      const content = await zip.generateAsync({ type: "blob" })
      const url = URL.createObjectURL(content)
      const link = document.createElement("a")
      link.href = url
      link.download = `${title?.replace(/[^a-z0-9]/gi, "_") || "website"}.zip`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: "Download started",
        description: "Your website has been packaged and downloaded.",
      })
    } catch (error) {
      toast({
        title: "Download failed",
        description: "There was an error creating the download package.",
        variant: "destructive",
      })
    }
  }

  const handleOpenInNewTab = () => {
    const newWindow = window.open()
    if (newWindow) {
      newWindow.document.write(htmlCode)
      newWindow.document.close()
    }
  }

  const getIframeWidth = () => {
    switch (viewMode) {
      case "mobile":
        return "375px"
      case "tablet":
        return "768px"
      default:
        return "100%"
    }
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Preview</span>
          {title && (
            <span className="text-xs text-gray-500">- {title}</span>
          )}
        </div>

        {/* View Mode Toggles */}
        <div className="flex items-center gap-1 p-1 bg-white rounded border">
          <Button
            variant={viewMode === "desktop" ? "default" : "ghost"}
            size="sm"
            onClick={() => setViewMode("desktop")}
            className="h-7 px-2"
          >
            <Monitor className="w-3 h-3" />
          </Button>
          <Button
            variant={viewMode === "tablet" ? "default" : "ghost"}
            size="sm"
            onClick={() => setViewMode("tablet")}
            className="h-7 px-2"
          >
            <Tablet className="w-3 h-3" />
          </Button>
          <Button
            variant={viewMode === "mobile" ? "default" : "ghost"}
            size="sm"
            onClick={() => setViewMode("mobile")}
            className="h-7 px-2"
          >
            <Smartphone className="w-3 h-3" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenInNewTab}
            className="text-xs"
          >
            <ExternalLink className="w-3 h-3 mr-1" />
            Open
          </Button>
        </div>
      </div>

      {/* Preview Area */}
      <div className="flex-1 bg-gray-100 flex items-center justify-center p-4">
        <div
          className="transition-all duration-300 bg-white shadow-lg border"
          style={{
            width: getIframeWidth(),
            maxWidth: "100%",
            height: "100%",
            maxHeight: "100%"
          }}
        >
          <iframe
            ref={iframeRef}
            srcDoc={htmlCode}
            className="w-full h-full border-0"
            title="Website Preview"
            sandbox="allow-scripts allow-same-origin"
          />
        </div>
      </div>
    </div>
  )
}
