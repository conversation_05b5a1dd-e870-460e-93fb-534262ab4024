"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, Loader2, Wand2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface PromptFormProps {
  onGenerate: (prompt: string) => Promise<void>
  isLoading: boolean
}

const examplePrompts = [
  "A modern landing page for a SaaS product with a hero section, features, and pricing",
  "A portfolio website for a photographer with a gallery and contact form",
  "A restaurant website with menu, location, and online ordering",
  "A blog homepage with featured articles and sidebar",
  "An e-commerce product page with images, description, and reviews"
]

export function PromptForm({ onGenerate, isLoading }: PromptFormProps) {
  const [prompt, setPrompt] = useState("")
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!prompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a prompt to generate a website.",
        variant: "destructive",
      })
      return
    }

    if (prompt.length > 1000) {
      toast({
        title: "Error",
        description: "Prompt is too long. Please keep it under 1000 characters.",
        variant: "destructive",
      })
      return
    }

    await onGenerate(prompt.trim())
  }

  const handleExampleClick = (example: string) => {
    setPrompt(example)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="w-5 h-5 text-blue-500" />
            Generate Website
          </CardTitle>
          <CardDescription>
            Describe the website you want to create and our AI will build it for you.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="prompt" className="text-sm font-medium">
                Website Description
              </label>
              <Textarea
                id="prompt"
                placeholder="Describe the website you want to create... (e.g., 'A modern landing page for a fitness app with hero section, features, and testimonials')"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="min-h-[120px] resize-none"
                disabled={isLoading}
              />
              <div className="text-xs text-muted-foreground text-right">
                {prompt.length}/1000 characters
              </div>
            </div>
            
            <Button 
              type="submit" 
              className="w-full" 
              disabled={isLoading || !prompt.trim()}
              size="lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Generate Website
                </>
              )}
            </Button>
          </form>

          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground">
              Try these examples:
            </h4>
            <div className="grid gap-2">
              {examplePrompts.map((example, index) => (
                <motion.button
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => handleExampleClick(example)}
                  disabled={isLoading}
                  className="text-left p-3 text-sm bg-muted/50 hover:bg-muted rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {example}
                </motion.button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
