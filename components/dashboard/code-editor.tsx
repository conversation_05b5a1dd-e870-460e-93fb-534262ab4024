"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Copy, Check, Download, RefreshCw } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface CodeEditorProps {
  code: string
  onChange: (code: string) => void
}

export function CodeEditor({ code, onChange }: CodeEditorProps) {
  const [localCode, setLocalCode] = useState(code)
  const [copied, setCopied] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    setLocalCode(code)
  }, [code])

  const handleCodeChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newCode = e.target.value
    setLocalCode(newCode)
    onChange(newCode)
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(localCode)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast({
        title: "Code copied!",
        description: "HTML code has been copied to your clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Failed to copy code to clipboard.",
        variant: "destructive",
      })
    }
  }

  const handleDownload = () => {
    const blob = new Blob([localCode], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'website.html'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: "File downloaded!",
      description: "HTML file has been saved to your downloads folder.",
    })
  }

  const handleReset = () => {
    setLocalCode(code)
    onChange(code)
    toast({
      title: "Code reset",
      description: "Code has been reset to the original generated version.",
    })
  }

  const lineCount = localCode.split('\n').length

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            HTML Code
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {lineCount} lines
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            className="text-xs"
          >
            <RefreshCw className="w-3 h-3 mr-1" />
            Reset
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="text-xs"
          >
            {copied ? (
              <Check className="w-3 h-3 mr-1 text-green-600" />
            ) : (
              <Copy className="w-3 h-3 mr-1" />
            )}
            {copied ? 'Copied!' : 'Copy'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            className="text-xs"
          >
            <Download className="w-3 h-3 mr-1" />
            Download
          </Button>
        </div>
      </div>

      {/* Code Editor */}
      <div className="flex-1 relative">
        <textarea
          value={localCode}
          onChange={handleCodeChange}
          className="w-full h-full p-4 font-mono text-sm bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-0 resize-none focus:outline-none focus:ring-0"
          style={{
            lineHeight: '1.5',
            tabSize: 2,
          }}
          spellCheck={false}
          placeholder="Your HTML code will appear here..."
        />
        
        {/* Line numbers overlay */}
        <div className="absolute left-0 top-0 p-4 pointer-events-none select-none">
          <div className="font-mono text-sm text-gray-400 dark:text-gray-600">
            {Array.from({ length: lineCount }, (_, i) => (
              <div key={i + 1} style={{ lineHeight: '1.5' }}>
                {i + 1}
              </div>
            ))}
          </div>
        </div>
        
        {/* Left padding for line numbers */}
        <style jsx>{`
          textarea {
            padding-left: ${Math.max(2, lineCount.toString().length) * 8 + 32}px !important;
          }
        `}</style>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-600 dark:text-gray-400">
        <div className="flex items-center gap-4">
          <span>HTML</span>
          <span>{localCode.length} characters</span>
          <span>{localCode.split(/\s+/).filter(word => word.length > 0).length} words</span>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>Ready</span>
        </div>
      </div>
    </div>
  )
}
