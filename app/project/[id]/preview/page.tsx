"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { redirect, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Download, Share2, Edit } from "lucide-react"
import Link from "next/link"
import { useToast } from "@/hooks/use-toast"
import JS<PERSON><PERSON> from "jszip"

interface Project {
  id: string
  title: string
  prompt: string
  htmlCode: string
  cssCode: string | null
  jsCode: string | null
  createdAt: string
  updatedAt: string
}

export default function ProjectPreviewPage({ params }: { params: { id: string } }) {
  const { data: session, status } = useSession()
  const [project, setProject] = useState<Project | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      redirect("/login")
    }

    if (status === "authenticated") {
      fetchProject()
    }
  }, [status, params.id])

  const fetchProject = async () => {
    try {
      const response = await fetch(`/api/projects/${params.id}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          toast({
            title: "Project not found",
            description: "The project you're looking for doesn't exist.",
            variant: "destructive",
          })
          router.push("/projects")
          return
        }
        throw new Error("Failed to fetch project")
      }

      const data = await response.json()
      setProject(data)
    } catch (error) {
      console.error("Fetch project error:", error)
      toast({
        title: "Error",
        description: "Failed to load project. Please try again.",
        variant: "destructive",
      })
      router.push("/projects")
    } finally {
      setIsLoading(false)
    }
  }

  const generateFullHTML = () => {
    if (!project) return ""
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${project.title}</title>
    ${project.cssCode ? `<style>${project.cssCode}</style>` : ""}
</head>
<body>
    ${project.htmlCode}
    ${project.jsCode ? `<script>${project.jsCode}</script>` : ""}
</body>
</html>`
  }

  const handleDownload = async () => {
    if (!project) return

    try {
      const zip = new JSZip()
      
      // Add files to zip
      zip.file("index.html", generateFullHTML())
      if (project.cssCode?.trim()) {
        zip.file("styles.css", project.cssCode)
      }
      if (project.jsCode?.trim()) {
        zip.file("script.js", project.jsCode)
      }
      
      // Add README
      const readme = `# ${project.title}

This project was created with WebGenie.

## Description
${project.prompt}

## Files:
- index.html - Main HTML file
${project.cssCode?.trim() ? "- styles.css - CSS styles" : ""}
${project.jsCode?.trim() ? "- script.js - JavaScript code" : ""}

## Usage:
Open index.html in your web browser to view the website.

Created with ❤️ using WebGenie
`
      zip.file("README.md", readme)
      
      // Generate and download
      const content = await zip.generateAsync({ type: "blob" })
      const url = URL.createObjectURL(content)
      const link = document.createElement("a")
      link.href = url
      link.download = `${project.title.replace(/[^a-z0-9]/gi, "_")}.zip`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: "Download started",
        description: "Your project has been packaged and downloaded.",
      })
    } catch (error) {
      toast({
        title: "Download failed",
        description: "There was an error creating the download package.",
        variant: "destructive",
      })
    }
  }

  const handleShare = () => {
    const url = window.location.href
    navigator.clipboard.writeText(url).then(() => {
      toast({
        title: "Link copied",
        description: "Preview link has been copied to your clipboard.",
      })
    }).catch(() => {
      toast({
        title: "Copy failed",
        description: "Failed to copy link to clipboard.",
        variant: "destructive",
      })
    })
  }

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Project not found</h1>
          <p className="text-muted-foreground">The project you're looking for doesn't exist.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="border-b bg-background p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/projects">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Projects
              </Link>
            </Button>
            <div>
              <h1 className="text-xl font-semibold">{project.title}</h1>
              <p className="text-sm text-muted-foreground">Preview Mode</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button variant="outline" size="sm" onClick={handleDownload}>
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button size="sm" asChild>
              <Link href={`/project/${project.id}`}>
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Preview */}
      <div className="flex-1 bg-gray-100 dark:bg-gray-900">
        <iframe
          srcDoc={generateFullHTML()}
          className="w-full h-full border-0"
          title={project.title}
          sandbox="allow-scripts allow-same-origin"
        />
      </div>
    </div>
  )
}
