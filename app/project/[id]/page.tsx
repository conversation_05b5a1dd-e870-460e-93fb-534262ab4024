"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { redirect, useRouter } from "next/navigation"
import { CodeEditor } from "@/components/editor/code-editor"
import { useToast } from "@/hooks/use-toast"

interface Project {
  id: string
  title: string
  prompt: string
  htmlCode: string
  cssCode: string | null
  jsCode: string | null
  createdAt: string
  updatedAt: string
}

export default function ProjectPage({ params }: { params: { id: string } }) {
  const { data: session, status } = useSession()
  const [project, setProject] = useState<Project | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      redirect("/login")
    }

    if (status === "authenticated") {
      fetchProject()
    }
  }, [status, params.id])

  const fetchProject = async () => {
    try {
      const response = await fetch(`/api/projects/${params.id}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          toast({
            title: "Project not found",
            description: "The project you're looking for doesn't exist.",
            variant: "destructive",
          })
          router.push("/dashboard")
          return
        }
        throw new Error("Failed to fetch project")
      }

      const data = await response.json()
      setProject(data)
    } catch (error) {
      console.error("Fetch project error:", error)
      toast({
        title: "Error",
        description: "Failed to load project. Please try again.",
        variant: "destructive",
      })
      router.push("/dashboard")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async (code: { html: string; css: string; js: string }) => {
    if (!project) return

    try {
      const response = await fetch(`/api/projects/${project.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          htmlCode: code.html,
          cssCode: code.css,
          jsCode: code.js,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to save project")
      }

      const updatedProject = await response.json()
      setProject(updatedProject)
      
      toast({
        title: "Project saved",
        description: "Your changes have been saved successfully.",
      })
    } catch (error) {
      console.error("Save project error:", error)
      toast({
        title: "Save failed",
        description: "Failed to save your changes. Please try again.",
        variant: "destructive",
      })
    }
  }

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Project not found</h1>
          <p className="text-muted-foreground">The project you're looking for doesn't exist.</p>
        </div>
      </div>
    )
  }

  return (
    <CodeEditor
      initialHtml={project.htmlCode}
      initialCss={project.cssCode || ""}
      initialJs={project.jsCode || ""}
      title={project.title}
      onSave={handleSave}
    />
  )
}
