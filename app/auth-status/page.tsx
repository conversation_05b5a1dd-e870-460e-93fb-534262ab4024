"use client"

import { useSession, signIn, signOut } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function AuthStatusPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    console.log("Auth Status:", status)
    console.log("Session Data:", session)
  }, [status, session])

  const handleSignIn = async (provider: string) => {
    console.log(`Signing in with ${provider}`)
    await signIn(provider, { callbackUrl: "/dashboard" })
  }

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/" })
  }

  const goToDashboard = () => {
    router.push("/dashboard")
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading authentication status...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-8 bg-background">
      <div className="max-w-2xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Status</CardTitle>
            <CardDescription>
              Current authentication state and user information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>Status:</strong> 
                <span className={`ml-2 px-2 py-1 rounded text-sm ${
                  status === "authenticated" ? "bg-green-100 text-green-800" :
                  status === "unauthenticated" ? "bg-red-100 text-red-800" :
                  "bg-yellow-100 text-yellow-800"
                }`}>
                  {status}
                </span>
              </div>
              <div>
                <strong>Session:</strong> {session ? "✓ Active" : "✗ None"}
              </div>
            </div>

            {session ? (
              <div className="space-y-4">
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                  <h3 className="font-semibold text-green-800 dark:text-green-200 mb-3">
                    ✅ Authenticated User
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>Name:</strong> {session.user?.name || "Not provided"}</div>
                    <div><strong>Email:</strong> {session.user?.email || "Not provided"}</div>
                    <div><strong>User ID:</strong> {(session.user as any)?.id || "Not provided"}</div>
                    {session.user?.image && (
                      <div className="flex items-center gap-2">
                        <strong>Avatar:</strong>
                        <img 
                          src={session.user.image} 
                          alt="User avatar" 
                          className="w-8 h-8 rounded-full"
                        />
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={goToDashboard} className="flex-1">
                    Go to Dashboard
                  </Button>
                  <Button onClick={handleSignOut} variant="outline">
                    Sign Out
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                  <h3 className="font-semibold text-red-800 dark:text-red-200 mb-2">
                    ❌ Not Authenticated
                  </h3>
                  <p className="text-sm text-red-600 dark:text-red-300">
                    Please sign in to access protected features.
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Button 
                    onClick={() => handleSignIn("google")}
                    className="w-full"
                  >
                    Sign in with Google
                  </Button>
                  <Button 
                    onClick={() => handleSignIn("github")}
                    className="w-full"
                    variant="outline"
                  >
                    Sign in with GitHub
                  </Button>
                </div>
              </div>
            )}

            <div className="mt-6 p-4 bg-muted rounded-lg">
              <h4 className="font-semibold mb-2">Debug Information</h4>
              <pre className="text-xs overflow-auto">
                {JSON.stringify({ status, session }, null, 2)}
              </pre>
            </div>

          </CardContent>
        </Card>
      </div>
    </div>
  )
}
