"use client"

import { useState } from "react"
import { ChatInterface } from "@/components/dashboard/chat-interface"
import { CodeEditor } from "@/components/dashboard/code-editor"
import { WebsitePreview } from "@/components/dashboard/website-preview"

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
}

const sampleHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Restaurant</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <h1 class="text-2xl font-bold text-gray-900">Delicious Dining</h1>
                <nav class="hidden md:flex space-x-8">
                    <a href="#" class="text-gray-500 hover:text-gray-900">Home</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">Menu</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">About</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">Contact</a>
                </nav>
            </div>
        </div>
    </header>

    <section class="bg-gradient-to-r from-orange-600 to-red-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h2 class="text-4xl md:text-6xl font-bold mb-6">Welcome to Delicious Dining</h2>
                <p class="text-xl md:text-2xl mb-8 text-orange-100">Experience culinary excellence with our carefully crafted dishes</p>
                <button class="bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    View Menu
                </button>
            </div>
        </div>
    </section>

    <section class="py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Specialties</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center p-6 rounded-lg border hover:shadow-lg transition-shadow">
                    <div class="text-4xl mb-4">🍽️</div>
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">Fresh Ingredients</h4>
                    <p class="text-gray-600">Locally sourced, organic ingredients in every dish</p>
                </div>
                <div class="text-center p-6 rounded-lg border hover:shadow-lg transition-shadow">
                    <div class="text-4xl mb-4">👨‍🍳</div>
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">Expert Chefs</h4>
                    <p class="text-gray-600">World-class culinary team with years of experience</p>
                </div>
                <div class="text-center p-6 rounded-lg border hover:shadow-lg transition-shadow">
                    <div class="text-4xl mb-4">🏆</div>
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">Award Winning</h4>
                    <p class="text-gray-600">Recognized for excellence in fine dining</p>
                </div>
            </div>
        </div>
    </section>
</body>
</html>`

export default function DemoDashboardPage() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: 'Hello! I\'m your AI web developer. Describe the website you\'d like me to create for you.',
      timestamp: new Date()
    },
    {
      id: '2',
      type: 'user',
      content: 'Create a modern restaurant website with a hero section and features',
      timestamp: new Date()
    },
    {
      id: '3',
      type: 'assistant',
      content: 'I\'ve created a beautiful restaurant website for you! It features a modern design with a hero section, navigation, and a features section highlighting your specialties. You can see the preview on the right and edit the code if needed.',
      timestamp: new Date()
    }
  ])
  const [activeTab, setActiveTab] = useState<'preview' | 'code'>('preview')
  const [htmlCode, setHtmlCode] = useState(sampleHTML)
  const [isLoading, setIsLoading] = useState(false)

  const handleSendMessage = (message: string) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, newMessage])
    
    // Simulate AI response
    setTimeout(() => {
      const response: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'I understand your request! Let me create that for you...',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, response])
    }, 1000)
  }

  const handleCodeChange = (newCode: string) => {
    setHtmlCode(newCode)
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <h1 className="text-xl font-semibold text-gray-900">
          Dashboard Demo - New Layout
        </h1>
        <p className="text-sm text-gray-500">
          Chat interface on the left, code editor and preview on the right
        </p>
      </div>
      
      <div className="flex-1 flex overflow-hidden">
        {/* Left Side - Chat Interface */}
        <div className="w-1/3 border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200 bg-white">
            <h2 className="text-lg font-semibold text-gray-900">AI Assistant</h2>
            <p className="text-sm text-gray-500">
              Describe your website and I'll build it for you
            </p>
          </div>
          
          <ChatInterface
            messages={messages}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
          />
        </div>

        {/* Right Side - Code Editor and Preview */}
        <div className="flex-1 flex flex-col">
          {/* Tab Navigation */}
          <div className="flex border-b border-gray-200 bg-white">
            <button
              onClick={() => setActiveTab('preview')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'preview'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Preview
            </button>
            <button
              onClick={() => setActiveTab('code')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'code'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Code
            </button>
            <div className="ml-auto p-2">
              <button className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                Download HTML
              </button>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-hidden">
            {activeTab === 'preview' ? (
              <WebsitePreview
                htmlCode={htmlCode}
                title="Modern Restaurant"
              />
            ) : (
              <CodeEditor
                code={htmlCode}
                onChange={handleCodeChange}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
