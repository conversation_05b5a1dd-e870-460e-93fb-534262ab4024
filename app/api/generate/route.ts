import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { generateWebsite } from "@/lib/vertex-ai"

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { prompt } = await req.json()

    if (!prompt || typeof prompt !== "string") {
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 })
    }

    if (prompt.length > 1000) {
      return NextResponse.json({ error: "Prompt too long" }, { status: 400 })
    }

    // Generate website using Vertex AI
    const htmlCode = await generateWebsite(prompt)

    if (!htmlCode) {
      return NextResponse.json({ error: "Failed to generate website" }, { status: 500 })
    }

    // Create a simple title from the prompt
    const title = prompt.slice(0, 100) + (prompt.length > 100 ? "..." : "")

    return NextResponse.json({
      id: Date.now().toString(), // Simple ID for now
      htmlCode,
      title,
    })
  } catch (error) {
    console.error("Generate API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
