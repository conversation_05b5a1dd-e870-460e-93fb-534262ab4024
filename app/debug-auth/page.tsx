import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default async function DebugAuthPage() {
  // Fetch providers from the API
  let providers = null
  try {
    const response = await fetch('http://localhost:3000/api/auth/providers', {
      cache: 'no-store'
    })
    providers = await response.json()
  } catch (error) {
    console.error('Failed to fetch providers:', error)
  }

  return (
    <div className="min-h-screen p-8 bg-background">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>NextAuth Debug Information</CardTitle>
            <CardDescription>
              Use this information to configure your OAuth providers correctly
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            
            <div>
              <h3 className="text-lg font-semibold mb-3">Environment Variables</h3>
              <div className="bg-muted p-4 rounded-lg space-y-2 font-mono text-sm">
                <div>NEXTAUTH_URL: {process.env.NEXTAUTH_URL || 'Not set'}</div>
                <div>GOOGLE_CLIENT_ID: {process.env.GOOGLE_CLIENT_ID ? '✓ Set' : '✗ Missing'}</div>
                <div>GOOGLE_CLIENT_SECRET: {process.env.GOOGLE_CLIENT_SECRET ? '✓ Set' : '✗ Missing'}</div>
                <div>GITHUB_CLIENT_ID: {process.env.GITHUB_CLIENT_ID ? '✓ Set' : '✗ Missing'}</div>
                <div>GITHUB_CLIENT_SECRET: {process.env.GITHUB_CLIENT_SECRET ? '✓ Set' : '✗ Missing'}</div>
                <div>NEXTAUTH_SECRET: {process.env.NEXTAUTH_SECRET ? '✓ Set' : '✗ Missing'}</div>
              </div>
            </div>

            {providers && (
              <div>
                <h3 className="text-lg font-semibold mb-3">OAuth Providers Configuration</h3>
                <div className="space-y-4">
                  {Object.entries(providers).map(([key, provider]: [string, any]) => (
                    <div key={key} className="bg-muted p-4 rounded-lg">
                      <h4 className="font-semibold text-base mb-2">{provider.name}</h4>
                      <div className="space-y-1 font-mono text-sm">
                        <div><strong>Provider ID:</strong> {provider.id}</div>
                        <div><strong>Sign-in URL:</strong> {provider.signinUrl}</div>
                        <div className="text-green-600"><strong>Callback URL:</strong> {provider.callbackUrl}</div>
                      </div>
                      <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border-l-4 border-yellow-400">
                        <p className="text-sm">
                          <strong>Configure this exact URL in your {provider.name} OAuth settings:</strong>
                        </p>
                        <code className="block mt-1 text-sm font-mono bg-white dark:bg-gray-800 p-2 rounded">
                          {provider.callbackUrl}
                        </code>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div>
              <h3 className="text-lg font-semibold mb-3">Quick Test Links</h3>
              <div className="space-y-2">
                <div>
                  <a 
                    href="/api/auth/signin/google" 
                    className="text-blue-600 hover:underline"
                    target="_blank"
                  >
                    Test Google OAuth → /api/auth/signin/google
                  </a>
                </div>
                <div>
                  <a 
                    href="/api/auth/signin/github" 
                    className="text-blue-600 hover:underline"
                    target="_blank"
                  >
                    Test GitHub OAuth → /api/auth/signin/github
                  </a>
                </div>
                <div>
                  <a 
                    href="/auth-test" 
                    className="text-blue-600 hover:underline"
                  >
                    Interactive Auth Test Page → /auth-test
                  </a>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-blue-400">
              <h4 className="font-semibold mb-2">Instructions:</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Copy the exact callback URLs shown above</li>
                <li>Add them to your Google Cloud Console and GitHub OAuth app settings</li>
                <li>Make sure there are no extra spaces or different ports</li>
                <li>Clear your browser cache and try signing in again</li>
              </ol>
            </div>

          </CardContent>
        </Card>
      </div>
    </div>
  )
}
