"use client"

import { useSession, signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function TestRedirectPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    console.log("Session status:", status)
    console.log("Session data:", session)
    
    if (status === "authenticated") {
      console.log("User is authenticated, redirecting to dashboard...")
      router.push("/dashboard")
    }
  }, [status, session, router])

  const handleSignIn = async (provider: string) => {
    console.log(`Attempting to sign in with ${provider}`)
    try {
      const result = await signIn(provider, { 
        callbackUrl: "/dashboard",
        redirect: true 
      })
      console.log("SignIn result:", result)
    } catch (error) {
      console.error("SignIn error:", error)
    }
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading session...</p>
        </div>
      </div>
    )
  }

  if (status === "authenticated") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card>
          <CardHeader>
            <CardTitle>Redirecting...</CardTitle>
            <CardDescription>You are authenticated. Redirecting to dashboard...</CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Test Authentication Redirect</CardTitle>
          <CardDescription>
            This page tests the authentication and redirect flow
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Button 
              onClick={() => handleSignIn("google")}
              className="w-full"
            >
              Sign in with Google
            </Button>
            <Button 
              onClick={() => handleSignIn("github")}
              className="w-full"
              variant="outline"
            >
              Sign in with GitHub
            </Button>
          </div>
          
          <div className="text-sm text-muted-foreground">
            <p><strong>Status:</strong> {status}</p>
            <p><strong>Session:</strong> {session ? "Yes" : "No"}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
