import { PredictionServiceClient } from '@google-cloud/aiplatform'
import { google } from '@google-cloud/aiplatform/build/protos/protos'

// Initialize Vertex AI client
const client = new PredictionServiceClient({
  apiEndpoint: `${process.env.VERTEX_AI_LOCATION || 'us-central1'}-aiplatform.googleapis.com`,
})

const project = process.env.GOOGLE_CLOUD_PROJECT
const location = process.env.VERTEX_AI_LOCATION || 'us-central1'
const publisher = 'google'
const model = 'gemini-1.5-pro-001'

export const SYSTEM_PROMPT = `You are a frontend developer. Return a fully working HTML file styled with Tailwind CSS. It must be responsive, semantic, and well-structured. Do not include explanations.

Requirements:
- Use Tailwind CSS classes for styling
- Make it responsive and mobile-first
- Use semantic HTML elements
- Include proper meta tags and viewport
- Use modern CSS Grid and Flexbox layouts
- Add hover effects and transitions
- Ensure accessibility with proper ARIA labels
- Use a cohesive color scheme
- Include proper typography hierarchy

Return only the complete HTML code without any explanations or markdown formatting.`

export async function generateWebsite(prompt: string): Promise<string> {
  try {
    if (!project) {
      throw new Error("GOOGLE_CLOUD_PROJECT environment variable is not set")
    }

    const endpoint = `projects/${project}/locations/${location}/publishers/${publisher}/models/${model}`

    const instanceValue = {
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `${SYSTEM_PROMPT}\n\nUser request: ${prompt}`
            }
          ]
        }
      ],
      generation_config: {
        temperature: 0.7,
        max_output_tokens: 4000,
        top_p: 0.8,
        top_k: 40
      }
    }

    const instances = [instanceValue].map(instance =>
      google.protobuf.Value.fromObject(instance)
    )

    const request = {
      endpoint,
      instances,
    }

    const [response] = await client.predict(request)

    if (!response.predictions || response.predictions.length === 0) {
      throw new Error("No predictions returned from Vertex AI")
    }

    const prediction = response.predictions[0]
    const content = prediction?.structValue?.fields?.candidates?.listValue?.values?.[0]?.structValue?.fields?.content?.structValue?.fields?.parts?.listValue?.values?.[0]?.structValue?.fields?.text?.stringValue

    if (!content) {
      // Fallback to sample HTML if Vertex AI response format is unexpected
      console.warn("Unexpected Vertex AI response format, using fallback")
      return generateSampleHTML(prompt)
    }

    return content
  } catch (error) {
    console.error("Vertex AI API error:", error)
    // Fallback to sample HTML generation for demo purposes
    console.warn("Falling back to sample HTML generation")
    return generateSampleHTML(prompt)
  }
}

// Sample HTML generator for demo purposes
function generateSampleHTML(prompt: string): string {
  const title = extractTitle(prompt)
  const theme = extractTheme(prompt)

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">${title}</h1>
                </div>
                <nav class="hidden md:flex space-x-8">
                    <a href="#" class="text-gray-500 hover:text-gray-900">Home</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">About</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">Services</a>
                    <a href="#" class="text-gray-500 hover:text-gray-900">Contact</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h2 class="text-4xl md:text-6xl font-bold mb-6">
                    Welcome to ${title}
                </h2>
                <p class="text-xl md:text-2xl mb-8 text-blue-100">
                    ${generateDescription(prompt)}
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        Get Started
                    </button>
                    <button class="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        Learn More
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Our Features
                </h3>
                <p class="text-xl text-gray-600">
                    Discover what makes us special
                </p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                ${generateFeatures(theme)}
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h4 class="text-2xl font-bold mb-4">${title}</h4>
                <p class="text-gray-400 mb-6">
                    Generated with AI-powered WebGenie
                </p>
                <div class="flex justify-center space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white">Privacy</a>
                    <a href="#" class="text-gray-400 hover:text-white">Terms</a>
                    <a href="#" class="text-gray-400 hover:text-white">Contact</a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>`
}

function extractTitle(prompt: string): string {
  const keywords = prompt.toLowerCase()
  if (keywords.includes('restaurant')) return 'Delicious Dining'
  if (keywords.includes('portfolio')) return 'Creative Portfolio'
  if (keywords.includes('blog')) return 'My Blog'
  if (keywords.includes('ecommerce') || keywords.includes('shop')) return 'Online Store'
  if (keywords.includes('saas') || keywords.includes('software')) return 'SaaS Platform'
  if (keywords.includes('agency')) return 'Digital Agency'
  if (keywords.includes('fitness') || keywords.includes('gym')) return 'Fitness Studio'
  return 'Modern Website'
}

function extractTheme(prompt: string): string {
  const keywords = prompt.toLowerCase()
  if (keywords.includes('restaurant') || keywords.includes('food')) return 'restaurant'
  if (keywords.includes('tech') || keywords.includes('software')) return 'tech'
  if (keywords.includes('creative') || keywords.includes('design')) return 'creative'
  if (keywords.includes('business') || keywords.includes('corporate')) return 'business'
  return 'general'
}

function generateDescription(prompt: string): string {
  const theme = extractTheme(prompt)
  const descriptions = {
    restaurant: 'Experience culinary excellence with our carefully crafted dishes and exceptional service.',
    tech: 'Innovative solutions powered by cutting-edge technology to transform your business.',
    creative: 'Bringing your creative vision to life with stunning design and artistic excellence.',
    business: 'Professional services designed to help your business grow and succeed.',
    general: 'Discover amazing experiences and exceptional quality in everything we do.'
  }
  return descriptions[theme as keyof typeof descriptions] || descriptions.general
}

function generateFeatures(theme: string): string {
  const features = {
    restaurant: [
      { icon: '🍽️', title: 'Fresh Ingredients', desc: 'Locally sourced, organic ingredients in every dish' },
      { icon: '👨‍🍳', title: 'Expert Chefs', desc: 'World-class culinary team with years of experience' },
      { icon: '🏆', title: 'Award Winning', desc: 'Recognized for excellence in fine dining' }
    ],
    tech: [
      { icon: '⚡', title: 'Lightning Fast', desc: 'Optimized performance for the best user experience' },
      { icon: '🔒', title: 'Secure', desc: 'Enterprise-grade security to protect your data' },
      { icon: '📱', title: 'Mobile Ready', desc: 'Responsive design that works on all devices' }
    ],
    creative: [
      { icon: '🎨', title: 'Creative Design', desc: 'Unique and innovative design solutions' },
      { icon: '💡', title: 'Fresh Ideas', desc: 'Original concepts that make you stand out' },
      { icon: '🚀', title: 'Fast Delivery', desc: 'Quick turnaround without compromising quality' }
    ],
    business: [
      { icon: '📈', title: 'Growth Focused', desc: 'Strategies designed to scale your business' },
      { icon: '🤝', title: 'Partnership', desc: 'We work closely with you to achieve your goals' },
      { icon: '💼', title: 'Professional', desc: 'Expert team with proven track record' }
    ],
    general: [
      { icon: '⭐', title: 'Quality', desc: 'Exceptional quality in everything we deliver' },
      { icon: '🎯', title: 'Focused', desc: 'Targeted solutions for your specific needs' },
      { icon: '💪', title: 'Reliable', desc: 'Dependable service you can count on' }
    ]
  }

  const themeFeatures = features[theme as keyof typeof features] || features.general

  return themeFeatures.map(feature => `
    <div class="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
      <div class="text-4xl mb-4">${feature.icon}</div>
      <h4 class="text-xl font-semibold text-gray-900 mb-2">${feature.title}</h4>
      <p class="text-gray-600">${feature.desc}</p>
    </div>
  `).join('')
}
